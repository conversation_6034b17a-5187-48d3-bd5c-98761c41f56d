<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings> 
  </PropertyGroup>



  <ItemGroup>
    <Compile Remove="Crystalaligner\**" />
    <Compile Remove="Queries\SingleQuery\Clinics\**" />
    <EmbeddedResource Remove="Crystalaligner\**" />
    <EmbeddedResource Remove="Queries\SingleQuery\Clinics\**" />
    <None Remove="Crystalaligner\**" />
    <None Remove="Queries\SingleQuery\Clinics\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Handlers\Orders\CreatePayTRPaymentHandler.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Handlers\Clinics\ClinicSelectHandler.cs~RF181d10b.TMP" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Commands\Clinics\" />
    <Folder Include="Requests\" />
    <Folder Include="PipelineBehaviors\" />
  </ItemGroup>

  <ItemGroup>
	  <PackageReference Include="AutoMapper" Version="12.0.1" />
	  <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
	  <PackageReference Include="FirebaseAdmin" Version="3.1.0" />
	  <PackageReference Include="FluentValidation" Version="11.4.0" />
	  <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.4.0" />
	  <PackageReference Include="MediatR" Version="9.0.0" />
	  <PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="9.0.0" />
	  <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
	  <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
	  <PackageReference Include="Microsoft.AspNetCore.JsonPatch" Version="6.0.8" />
	  <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="7.0.0" />
	  <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="7.0.0" />
	  <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.26.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Crystalaligner.Domain\Crystalaligner.Domain.csproj" />
    <ProjectReference Include="..\Crystalaligner.Model\Crystalaligner.Model.csproj" />
  </ItemGroup>

</Project>
