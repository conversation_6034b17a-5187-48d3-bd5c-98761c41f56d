version: "3.8"
services:
  db:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: crystalaligner-db
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=************
    ports:
      - "1433:1433"
    volumes:
      - mssql_data:/var/opt/mssql
    restart: unless-stopped

  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: crystalaligner-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Server=db;Database=CrystalAligner;User=sa;Password=************;TrustServerCertificate=True;
    ports:
      - "5000:5000"
    depends_on:
      - db
    restart: unless-stopped

  fe:
    image: buraxtaa/crystaldoctor:latest
    container_name: crystalaligner-fe
    ports:
      - "80:80"
    depends_on:
      - api
    restart: unless-stopped

volumes:
  mssql_data:
