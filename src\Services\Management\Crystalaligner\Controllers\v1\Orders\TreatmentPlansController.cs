﻿using Crystalaligner.Application.Commands.Orders;
using Crystalaligner.Application.Queries.MultipleQuery.Orders;
using Crystalaligner.Application.Queries.SingleQuery.Orders;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Helpers.Pagination;
using Crystalaligner.Core.Extensions;
using Crystalaligner.Core.Extensions.FilterExtensions;
using Crystalaligner.Management.Domain.Entities.Order.Orders;
using Crystalaligner.Tools;
using FluentFTP;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1.Orders;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class TreatmentPlansController : BaseController
{
    private readonly IMediator _mediatr;
    public static IConfiguration _configuration { get; set; }

    public TreatmentPlansController(IMediator mediatr, IConfiguration configuration)
    {
        _mediatr = mediatr;
        _configuration = configuration;
    }

    [HttpGet("GetExcelFileAsync")]
    public async Task<IActionResult> GetExcelFileAsync([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<TreatmentPlan>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new TreatmentPlansPagedQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        var excelFile = CreateExcelFile(result.Data);
        return File(excelFile, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "Tedavi_Listesi.xlsx");
    }

    [HttpGet]
    public async Task<IActionResult> Get()
    {
        var includes = new string[] { };

        Expression<Func<TreatmentPlan, bool>> predicate = s => s.Id > 0;

        var query = new TreatmentPlansQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> Get(int id)
    {
        Expression<Func<TreatmentPlan, bool>> predicate = s => s.Id == id;

        var includes = new string[] { };

        var query = new TreatmentPlanQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetByOrderId/{id}")]
    public async Task<IActionResult> GetByOrderId(int id)
    {
        Expression<Func<TreatmentPlan, bool>> predicate = s => s.OrderId == id;

        var includes = new string[] { };

        var query = new TreatmentPlansQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetPaged")]
    public async Task<IActionResult> GetPaged([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<TreatmentPlan>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new TreatmentPlansPagedQuery(request);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }


    [HttpPost]
    public async Task<IActionResult> Post([FromBody] TreatmentPlanCreate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPut("Put")]
    [Consumes("multipart/form-data")]
    public async Task<IActionResult> Put(
    IFormFile filePdfUrl, IFormFile filePdfUrlSecond, IFormFile filePdfUrlThird,
    [FromForm] int Id, [FromForm] int OrderId, [FromForm] bool IsApproved,
    [FromForm] string Reason, [FromForm] string VideoUrl,
    [FromForm] string Comment, [FromForm] string AnimationUrl)
    {
        var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
        string ftpServer = cdnConfig.FtpServer;
        string username = cdnConfig.Username;
        string password = cdnConfig.Password;
        string baseUrl = cdnConfig.TreatmentPlansUrl;

        var command = new TreatmentPlanUpdate();
        var client = new FtpClient(ftpServer, username, password, 21);

        string orderDirectoryPath = $"{baseUrl}{OrderId}";
        if (!client.DirectoryExists(orderDirectoryPath[1..]))
            client.CreateDirectory(orderDirectoryPath);

        client.Connect();

        IFormFile[] files = { filePdfUrl, filePdfUrlSecond, filePdfUrlThird };
        string[] pdfUrls = { "PdfUrl", "PdfUrlSecond", "PdfUrlThird" };

        for (int i = 0; i < files.Length; i++)
        {
            if (files[i] != null && files[i].Length > 0)
            {
                string tempFilePath = Path.GetTempFileName();
                using (var stream = new FileStream(tempFilePath, FileMode.Create))
                {
                    await files[i].CopyToAsync(stream);
                }

                string timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
                string newFileName = $"{timestamp}-{Path.GetFileName(files[i].FileName)}";

                if (!client.FileExists($"{orderDirectoryPath}/{files[i].FileName}"))
                {
                    client.UploadFile(tempFilePath, $"{orderDirectoryPath}/{newFileName}");
                }

                System.IO.File.Delete(tempFilePath); // Cleanup the temp file

                switch (i)
                {
                    case 0:
                        command.PdfUrl = $"{orderDirectoryPath[1..]}/{newFileName}";
                        break;
                    case 1:
                        command.PdfUrlSecond = $"{orderDirectoryPath[1..]}/{newFileName}";
                        break;
                    case 2:
                        command.PdfUrlThird = $"{orderDirectoryPath[1..]}/{newFileName}";
                        break;
                }
            }
        }

        client.Disconnect();

        // Set other command properties
        command.Id = Id;
        command.AnimationUrl = AnimationUrl;
        command.Comment = Comment;
        command.VideoUrl = VideoUrl;
        command.IsApproved = IsApproved;
        command.Reason = Reason;
        command.OrderId = OrderId;

        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }



    [HttpPut("Approve")]
    public async Task<IActionResult> Approve([FromBody] TreatmentPlanApproveUpdate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        Expression<Func<TreatmentPlan, bool>> predicate = s => s.Id == id;

        var query = new TreatmentPlanDelete(predicate);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
}

