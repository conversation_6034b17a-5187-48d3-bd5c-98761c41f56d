<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="wwwroot\images\**" />
    <Content Remove="wwwroot\images\**" />
    <EmbeddedResource Remove="wwwroot\images\**" />
    <None Remove="wwwroot\images\**" />
    <Compile Remove="..\Crystalaligner.Application\Commands\**" />
    <EmbeddedResource Remove="..\Crystalaligner.Application\Commands\**" />
    <None Remove="..\Crystalaligner.Application\Commands\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Controllers\v1\PayTr\PayController.cs" />
  </ItemGroup>

  <ItemGroup>
    <_WebToolingArtifacts Remove="Properties\PublishProfiles\Staging.pubxml" />
  </ItemGroup>
	<ItemGroup>
		<PackageReference Include="FluentFTP" Version="52.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.8" />
		<PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
		<PackageReference Include="Microsoft.AspNetCore.JsonPatch" Version="6.0.8" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.8" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.8">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.11.1" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />

	</ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="7.0.10" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controllers\v1\Blogs\" />
    <Folder Include="wwwroot\css\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Shared\Crystalaligner.Core\Crystalaligner.Core.csproj" />
    <ProjectReference Include="..\Crystalaligner.Application\Crystalaligner.Application.csproj" />
    <ProjectReference Include="..\Crystalaligner.Domain\Crystalaligner.Domain.csproj" />
    <ProjectReference Include="..\Crystalaligner.Infrastructure\Crystalaligner.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.Staging.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

</Project>
