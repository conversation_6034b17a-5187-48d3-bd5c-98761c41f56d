using System.Net;
using System.Reflection;
using System.Text;
using Crystalaligner.Application;
using Crystalaligner.Core.Attributes;
using Crystalaligner.Core.Base.Helpers.EmailHelper;
using Crystalaligner.Core.Extensions;
using Crystalaligner.Core.Middlewares;
using Crystalaligner.Core.Security;
using Crystalaligner.Management.Infrastructure;
using Crystalaligner.Tools;
using FirebaseAdmin;
using Google.Apis.Auth.OAuth2;
using Microsoft.AspNetCore.Mvc.Versioning;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Crystalaligner.Management;

public class Startup
{
    private readonly string MyAllowedOrigins = "_myAllowedOrigins";

    public Startup(IConfiguration configuration)
    {
        Configuration = configuration;
    }

    public IConfiguration Configuration { get; }

    // This method gets called by the runtime. Use this method to add services to the container.
    public void ConfigureServices(IServiceCollection services)
    {
        var IsProduction =
            Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == Environments.Production;
        if (IsProduction)
        {
            services.AddHttpsRedirection(options =>
            {
                options.HttpsPort = 443;
            });
        }

        services.Configure<IISServerOptions>(options =>
        {
            options.MaxRequestBodySize = long.MaxValue;
        });
        services.AddControllers().AddNewtonsoftJson();

        services
            .AddControllersWithViews()
            .AddNewtonsoftJson(opt =>
            {
                opt.SerializerSettings.ContractResolver = new DefaultContractResolver();
                opt.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
                opt.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Local;
                opt.SerializerSettings.Culture = System.Globalization.CultureInfo.GetCultureInfo(
                    "tr-TR"
                );
            });

        // const string MyAllowedOrigins = "_myAllowedOrigins";
        services.AddCors(options =>
        {
            options.AddPolicy(
                name: MyAllowedOrigins,
                policy =>
                {
                    // API'nize istek yapmasına izin verdiğiniz alan adları:
                    policy
                        .WithOrigins(
                            "http://api.crystalaligner.com", 
                            "http://lab.crystalaligner.com",
                            "https://api.crystalaligner.com", 
                            "https://lab.crystalaligner.com",
                            "http://crystalaligner.com",
                            "https://crystalaligner.com",
                            "http://api.buraxta.com", 
                            "http://lab.buraxta.com",
                            "http://doctor.buraxta.com", 
                            "http://doctor.buraxta.com"
                        ) // Frontend uygulamanız için
                        .AllowAnyHeader()
                        .AllowAnyMethod();
                }
            );
        });

        services.Configure<TokenSettings>(Configuration.GetSection("JWT"));

        services.AddTransient<ITokenSettings>(serviceProvider =>
            serviceProvider.GetRequiredService<IOptions<TokenSettings>>().Value
        );

        services.AddInfrastructure(Configuration);

        services.AddApplication();

        services.AddResponseCompression(opt => opt.EnableForHttps = true);

        services.Configure<BrotliCompressionProviderOptions>(opt =>
            opt.Level = System.IO.Compression.CompressionLevel.Fastest
        );

        services.AddSwaggerGen(swagger =>
        {
            swagger.SwaggerDoc("v1", new OpenApiInfo { Title = "Crystalaligner", Version = "v1" });
            swagger.SwaggerDoc("v2", new OpenApiInfo { Title = "Crystalaligner", Version = "v2" });
            swagger.OperationFilter<OpenApiParameterIgnoreFilter>();
            swagger.OperationFilter<FileUploadFilter>();

            // To Enable authorization using Swagger (JWT)
            swagger.AddSecurityDefinition(
                "Bearer",
                new OpenApiSecurityScheme()
                {
                    Name = "Bearer",
                    Type = SecuritySchemeType.Http,
                    Scheme = "bearer",
                    BearerFormat = "JWT",
                    In = ParameterLocation.Header,
                    Description =
                        "JWT Authorization header using the Bearer scheme. \r\n\r\n Enter Your token in the text input below.\r\n\r\nExample: \"12345abcdef\"",
                }
            );
            swagger.AddSecurityRequirement(
                new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer",
                            },
                        },
                        new[] { "Bearer" }
                    },
                }
            );
            swagger.AddSecurityDefinition(
                "Basic",
                new OpenApiSecurityScheme()
                {
                    Name = "Basic",
                    Type = SecuritySchemeType.Http,
                    Scheme = "Basic",
                    In = ParameterLocation.Header,
                    Description = "Basic Authorization header using the Basic scheme. ",
                }
            );
            swagger.AddSecurityRequirement(
                new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Basic",
                            },
                        },
                        new[] { "Basic" }
                    },
                }
            );
        });

        services.AddApiVersioning(opt =>
        {
            opt.AssumeDefaultVersionWhenUnspecified = false; //false olursa api versiyon göndermezsek çalışmaz
            opt.ReportApiVersions = true;
            opt.ApiVersionReader = ApiVersionReader.Combine(
                new UrlSegmentApiVersionReader(),
                new MediaTypeApiVersionReader("Version") //giden istekte api versiyonunu görmemizi sağlar
            );
        });

        services.AddVersionedApiExplorer(config =>
        {
            config.GroupNameFormat = "'v'VVV";
            config.SubstituteApiVersionInUrl = true;
        });

        var tokenOptions = Configuration.GetSection("JWT").Get<TokenSettings>();
        var signInKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(tokenOptions.SecurityKey));
        services
            .AddAuthentication(option =>
            {
                option.DefaultAuthenticateScheme = tokenOptions.ProviderKey;
                option.DefaultChallengeScheme = tokenOptions.ProviderKey;
            })
            .AddJwtBearer(
                tokenOptions.ProviderKey,
                options =>
                {
                    options.RequireHttpsMetadata = false;
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateAudience = true,
                        ValidateIssuer = true,
                        ValidateLifetime = true,
                        ValidateIssuerSigningKey = true,
                        ValidIssuer = tokenOptions.Issuer,
                        ValidAudience = tokenOptions.Audience,
                        IssuerSigningKey = signInKey,
                        ClockSkew = TimeSpan.Zero,
                        RequireExpirationTime = true,
                    };
                }
            );
        var defaultApp = FirebaseApp.Create(
            new AppOptions()
            {
                Credential = GoogleCredential.FromFile(
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "key.json")
                ),
            }
        );
    }

    // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
    public void Configure(
        IApplicationBuilder app,
        IWebHostEnvironment env,
        IHttpContextAccessor http
    )
    {
        //swagger Basic Auth
        app.UseMiddleware<SwaggerAuthMiddleware>();
        if (env.IsDevelopment() || env.IsStaging())
        {
            app.UseDeveloperExceptionPage();
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "Crystalaligner v1");
                c.SwaggerEndpoint("/swagger/v2/swagger.json", "Crystalaligner v2");
                c.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None);
            });
        }
        else if (env.IsProduction())
        {
            app.UseDeveloperExceptionPage();
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "Crystalaligner v1");
                c.SwaggerEndpoint("/swagger/v2/swagger.json", "Crystalaligner v2");
                c.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None);
            });
            app.UseHttpsRedirection();
            
        }

        app.UseResponseCompression();

        // app.UseCors(x =>
        //     x.AllowAnyMethod()
        //         .AllowAnyHeader()
        //         .SetIsOriginAllowed(origin => true) // allow any origin
        //         .AllowCredentials()
        // );

        app.UseRouting();

        app.UseStaticFiles();

        app.UseCors(MyAllowedOrigins);

        app.UseAuthentication();

        app.UseAuthorization();

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
        });

        //Seed database
        AppDbInitializer.Seed(app);

        http.UseHttpContextAccessor(Configuration); 

        UserExtensions.UserConfigure(http);
    }
}
